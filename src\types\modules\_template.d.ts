// 业务模块类型声明模板
// 复制此文件并重命名为具体的业务模块名称
// 例如：product.d.ts, order.d.ts, payment.d.ts 等

declare namespace API.ModuleName {
  /** 基础实体接口 */
  interface IEntity {
    /** 实体ID */
    id: string | number;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  }

  /** 列表响应接口 */
  interface IListResponse<T = IEntity> {
    /** 数据列表 */
    list: T[];
    /** 总数量 */
    total: number;
    /** 当前页码 */
    current: number;
    /** 每页大小 */
    size: number;
  }

  /** 详情响应接口 */
  interface IDetailResponse<T = IEntity> {
    /** 详细数据 */
    data: T;
  }

  /** 创建请求接口 */
  interface ICreateRequest {
    // 根据业务需求定义具体字段
  }

  /** 更新请求接口 */
  interface IUpdateRequest {
    // 根据业务需求定义具体字段
  }

  /** 查询参数接口 */
  interface IQueryParams extends API.Common.PaginatingCommonParams {
    /** 搜索关键词 */
    keyword?: string;
    /** 状态筛选 */
    status?: string;
    // 根据业务需求添加其他查询参数
  }
}
