import { forwardRef } from 'react';

import Image from 'next/image';

import { motion } from 'framer-motion';

import { getImageUrl } from '@/constants/cdn';

interface HeroSectionProps {
  scrollToNext: () => void;
  onViewportEnter?: () => void;
  viewport?: { amount?: number | 'some' | 'all' }; // 直接定义 viewport 属性类型
}

const HeroSection = forwardRef<HTMLDivElement, HeroSectionProps>(
  ({ scrollToNext }, ref) => {
    const subtitleText = '以AI重构旅行体验的智慧出行平台';

    return (
      <section
        ref={ref}
        className="relative h-screen w-full flex flex-col items-center justify-center snap-start overflow-hidden box-border"
      >
        {/* 背景图 */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src={getImageUrl('backgroupd.png')}
            alt="Background"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* 遮罩层，让文字更清晰 */}
        <div className="absolute inset-0 bg-black opacity-30" />

        {/* 中心内容 */}
        <div className="relative z-10 text-center">
          <motion.div className="relative w-[500px] h-[140px] mb-8">
            <Image
              src={getImageUrl('lingyou.png')}
              alt="翎游"
              fill
              className="object-contain"
              priority
            />
          </motion.div>
          <motion.div>
            <p
              style={{ color: 'rgba(255, 255, 255)' }}
              className="text-[24px] font-light"
            >
              {subtitleText}
            </p>
          </motion.div>
        </div>

        {/* 滚动箭头 */}
        <motion.div
          style={{ bottom: '54px' }}
          className="absolute cursor-pointer z-20"
          onClick={scrollToNext}
        >
          <Image
            src={getImageUrl('acrros.png')}
            alt="向下滚动"
            width={50}
            height={50}
            className="object-contain"
          />
        </motion.div>
      </section>
    );
  }
);

HeroSection.displayName = 'HeroSection'; // 推荐为 forwardRef 组件设置 displayName

export default HeroSection;
