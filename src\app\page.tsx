'use client'; // This directive is necessary for client-side hooks like useRef and useEffect

import { useRef } from 'react'; // 导入 useEffect

import { useSection } from '@/hooks/useSection'; // 导入 useSection 钩子

import AboutSection from '../components/AboutSection';
import BusinessSection from '../components/BusinessSection';
import HeroSection from '../components/HeroSection';

const Home = () => {
  const mainRef = useRef<HTMLElement>(null); // 为 main 元素添加 ref
  const sections = useRef<Array<HTMLDivElement | null>>([]);
  const { currentSectionIndex, setCurrentSectionIndex } = useSection(); // 从 Context 获取状态和 setter

  const totalSections = 3; // Hero, About, Business

  // Function to scroll to a specific section by index
  const scrollToSection = (index: number) => {
    if (sections.current[index]) {
      sections.current[index]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
      setCurrentSectionIndex(index);
    }
  };

  // Scroll down to the next section
  const scrollToNext = () => {
    const nextIndex = currentSectionIndex + 1;
    if (nextIndex < totalSections) {
      scrollToSection(nextIndex);
    }
  };

  // Scroll up to the previous section
  const scrollToPrev = () => {
    const prevIndex = currentSectionIndex - 1;
    if (prevIndex >= 0) {
      scrollToSection(prevIndex);
    }
  };

  return (
    <>
      <main
        ref={mainRef}
        className="h-screen w-screen overflow-y-scroll snap-y snap-mandatory scroll-smooth overflow-x-hidden"
        onScroll={() => {
          const mainElement = mainRef.current;
          if (!mainElement) return;

          const scrollPosition = mainElement.scrollTop;
          const viewportHeight = mainElement.clientHeight;
          const newIndex = Math.round(scrollPosition / viewportHeight);

          if (newIndex !== currentSectionIndex) {
            setCurrentSectionIndex(newIndex);
          }
        }}
      >
        <HeroSection
          ref={el => {
            sections.current[0] = el;
          }}
          scrollToNext={scrollToNext}
        />
        <AboutSection
          ref={el => {
            sections.current[1] = el;
          }}
          scrollToNext={scrollToNext}
          scrollToPrev={scrollToPrev}
        />
        <BusinessSection
          ref={el => {
            sections.current[2] = el;
          }}
          scrollToPrev={scrollToPrev}
        />
      </main>
    </>
  );
};

export default Home;
