import { forwardRef, useEffect, useState } from 'react';

import Image from 'next/image';

import { motion } from 'framer-motion';

import { getImageUrl } from '@/constants/cdn';
import { ABOUT_TEXTS } from '@/constants/texts';

import AboutSectionMobile from './AboutSectionMobile';
import <PERSON>roll<PERSON>rrow from './ScrollArrow';

interface AboutSectionProps {
  scrollToNext: () => void;
  scrollToPrev: () => void;
  onViewportEnter?: () => void;
  viewport?: { amount?: number | 'some' | 'all' }; // 添加 viewport 属性
}

const AboutSection = forwardRef<HTMLDivElement, AboutSectionProps>(
  ({ scrollToNext, scrollToPrev }, ref) => {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth <= 768);
      };

      checkMobile();
      window.addEventListener('resize', checkMobile);

      return () => {
        window.removeEventListener('resize', checkMobile);
      };
    }, []);

    if (isMobile) {
      return (
        <AboutSectionMobile
          scrollToNext={scrollToNext}
          scrollToPrev={scrollToPrev}
          ref={ref}
        />
      );
    }

    const sectionVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: { duration: 0.3, ease: 'easeOut' },
      },
    };

    const textVariants = {
      hidden: { opacity: 0, x: '100vw' },
      visible: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 0.6,
          delay: 0.3,
          ease: [0.4, 0, 0.2, 1],
        },
      },
    };

    const imageVariants = {
      hidden: { opacity: 0, x: '-100vw' },
      visible: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 0.6,
          delay: 0.3,
          ease: [0.4, 0, 0.2, 1],
        },
      },
    };

    return (
      <section
        ref={ref}
        className="relative h-screen w-full bg-white flex items-center justify-center snap-start"
      >
        <motion.div
          className="w-full h-full flex items-center justify-center px-20"
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ amount: 0.3 }}
        >
          <div className="max-w-[1310px] w-full mx-auto flex flex-row items-center justify-between">
            {/* 左侧图片 */}
            <motion.div
              className="flex-shrink-0 w-[641.6px] flex justify-center items-center"
              variants={imageVariants}
            >
              <div className="relative w-full h-[400px]">
                <Image
                  src={getImageUrl('about.png')}
                  alt="关于我们"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </motion.div>
            {/* 右侧文案 */}
            <motion.div
              className="flex-shrink-0 w-[514px] flex flex-col justify-center items-start pl-12"
              variants={textVariants}
            >
              <h2 className="text-[36px] font-bold mb-[20px] text-[#11111E]">
                {ABOUT_TEXTS.title}
              </h2>
              {ABOUT_TEXTS.content.map((text, index) => (
                <p
                  key={index}
                  className="text-[14px] leading-[22px] mb-[16px] text-[#66666E] mt-[0px]"
                >
                  {text}
                </p>
              ))}
            </motion.div>
          </div>
        </motion.div>
        {/* 滚动箭头 */}
        <ScrollArrow
          direction="down"
          onClick={scrollToNext}
          className="bottom-8"
        />
        <ScrollArrow direction="up" onClick={scrollToPrev} className="top-8" />
      </section>
    );
  }
);

AboutSection.displayName = 'AboutSection';

export default AboutSection;
