import { CDN_BASE_URL } from './src/constants/cdn';

import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'hero-pattern': `url('${CDN_BASE_URL}/bg-hero.jpg')`,
      },
      colors: {
        'primary-blue': '#4A90E2', // 示例主蓝色
        'secondary-gray': '#F2F4F7', // 示例背景灰色
        'text-dark': '#333333',
      },
    },
  },
  plugins: [],
};

export default config;
