worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       443 ssl;  # 启用 SSL 并监听 443 端口[1,4,6](@ref)
        server_name  localhost;

        # 关键 SSL 配置：指定证书和私钥路径（需替换为实际路径）
        ssl_certificate      /root/www.aitrip123.com/fullchain.pem;  # 证书文件路径（.crt 或 .pem）[1,6](@ref)
        ssl_certificate_key  /root/www.aitrip123.com/privkey.pem;     # 私钥文件路径（.key）[1,6](@ref)

        # 可选 SSL 优化配置
        ssl_protocols       TLSv1.2 TLSv1.3;  # 推荐协议版本[4,10](@ref)
        ssl_ciphers         HIGH:!aNULL:!MD5;   # 安全加密套件[6,10](@ref)
        ssl_session_cache   shared:SSL:10m;     # 会话缓存提高性能[1,6](@ref)
        ssl_session_timeout 10m;                # 会话超时时间

        # IOS Universal Links(通用链接)
        location /apple-app-site-association {
            default_type application/json;
            alias /home/<USER>/apple-app-site-association;
        }

        location / {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;  # 确保后端获取真实协议（https）
        }

        # 错误页面配置
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    # 新增 HTTP 重定向到 HTTPS（可选但推荐）
    server {
        listen       80;
        server_name  localhost;
        return       301 https://$host$request_uri;  # 80 端口请求重定向到 HTTPS[2,9](@ref)
    }

}
