#!/bin/bash
set -e # 任何命令失败则退出

# 日志函数
log() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"; }
err() { log "错误: $1"; exit 1; }
debug() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $1"; }

# 系统信息收集
collect_system_info() {
    log "收集系统信息..."

    # 操作系统信息
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        debug "操作系统: $NAME $VERSION"
    fi

    # SELinux 状态
    if command -v getenforce &>/dev/null; then
        debug "SELinux 状态: $(getenforce)"
    fi

    # Nginx 信息
    if command -v nginx &>/dev/null; then
        debug "Nginx 版本: $(nginx -v 2>&1)"
        debug "Nginx 进程用户: $(ps aux | grep nginx | grep -v grep | awk '{print $1}')"
    fi

    # 目录权限信息
    debug "out 目录权限: $(ls -la $OUT_DIR)"
    debug "out 目录 SELinux 上下文: $(ls -Z $OUT_DIR 2>/dev/null || echo 'SELinux 上下文不可用')"

    # Nginx 配置信息
    debug "Nginx 配置文件位置: $(nginx -t 2>&1 | grep 'nginx.conf')"
    debug "Nginx 错误日志位置: /var/log/nginx/error.log"

    log "系统信息收集完成"
}

# 查找 out 目录
find_out_directory() {
    log "查找 out 目录..."

    # 首先检查当前目录
    if [ -d "out" ]; then
        OUT_DIR="$(pwd)/out"
        log "在当前目录找到 out 目录: $OUT_DIR"
        return 0
    fi

    # 检查上级目录
    if [ -d "../out" ]; then
        OUT_DIR="$(cd .. && pwd)/out"
        log "在上级目录找到 out 目录: $OUT_DIR"
        return 0
    fi

    # 检查 /home/<USER>/app/out
    if [ -d "/home/<USER>/app/out" ]; then
        OUT_DIR="/home/<USER>/app/out"
        log "在 /home/<USER>/app 找到 out 目录: $OUT_DIR"
        return 0
    fi

    # 如果都没找到，尝试在项目根目录查找
    PROJECT_ROOT=$(git rev-parse --show-toplevel 2>/dev/null || pwd)
    if [ -d "$PROJECT_ROOT/out" ]; then
        OUT_DIR="$PROJECT_ROOT/out"
        log "在项目根目录找到 out 目录: $OUT_DIR"
        return 0
    fi

    err "无法找到 out 目录"
}

# 获取 Nginx 用户
get_nginx_user() {
    # 检查常见的 Nginx 用户
    if id nginx &>/dev/null; then
        echo "nginx"
    elif id www-data &>/dev/null; then
        echo "www-data"
    else
        # 在阿里云环境中，Nginx 可能以 root 用户运行
        echo "root"
    fi
}

# 检查 SELinux 状态
check_selinux() {
    if command -v getenforce &>/dev/null; then
        SELINUX_STATUS=$(getenforce)
        log "SELinux 状态: $SELINUX_STATUS"
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            log "SELinux 处于强制模式，将设置适当的上下文"
            return 0
        fi
    fi
    return 1
}

# 设置 SELinux 上下文
set_selinux_context() {
    if command -v chcon &>/dev/null; then
        log "设置 SELinux 上下文..."
        # 设置 httpd_sys_content_t 上下文
        sudo chcon -R -t httpd_sys_content_t "$OUT_DIR" || log "警告: 无法设置 SELinux 上下文"
        # 设置 httpd_sys_rw_content_t 上下文（如果需要写入权限）
        sudo chcon -R -t httpd_sys_rw_content_t "$OUT_DIR" || log "警告: 无法设置 SELinux 上下文"

        # 记录设置后的上下文
        debug "设置后的 SELinux 上下文: $(ls -Z $OUT_DIR 2>/dev/null || echo '无法获取 SELinux 上下文')"
    fi
}

# 验证目录和权限
verify_directory() {
    log "验证目录和权限..."

    # 检查目录是否存在
    if [ ! -d "$OUT_DIR" ]; then
        err "out 目录不存在: $OUT_DIR"
    fi

    # 检查 index.html 是否存在
    if [ ! -f "$OUT_DIR/index.html" ]; then
        err "index.html 文件不存在于 $OUT_DIR"
    fi

    # 获取 Nginx 用户
    NGINX_USER=$(get_nginx_user)
    log "检测到 Nginx 用户: $NGINX_USER"

    # 设置所有父目录的权限
    CURRENT_DIR="$OUT_DIR"
    while [ "$CURRENT_DIR" != "/" ]; do
        log "设置目录权限: $CURRENT_DIR"
        sudo chmod 755 "$CURRENT_DIR" || err "无法设置目录权限: $CURRENT_DIR"
        if [ "$NGINX_USER" != "root" ]; then
            sudo chown $NGINX_USER:$NGINX_USER "$CURRENT_DIR" || err "无法设置目录所有者: $CURRENT_DIR"
        fi
        CURRENT_DIR=$(dirname "$CURRENT_DIR")
    done

    # 设置目录权限
    log "设置目录权限..."
    if [ "$NGINX_USER" = "root" ]; then
        # 如果是 root 用户，只需要确保目录可读
        sudo chmod -R 755 "$OUT_DIR" || err "无法设置目录权限"
    else
        # 如果是其他用户，设置所有者和权限
        sudo chown -R $NGINX_USER:$NGINX_USER "$OUT_DIR" || err "无法设置目录所有者"
        sudo chmod -R 755 "$OUT_DIR" || err "无法设置目录权限"

        # 确保 _next 目录权限正确
        if [ -d "$OUT_DIR/_next" ]; then
            sudo chmod -R 755 "$OUT_DIR/_next" || err "无法设置 _next 目录权限"
            sudo chown -R $NGINX_USER:$NGINX_USER "$OUT_DIR/_next" || err "无法设置 _next 目录所有者"
        fi
    fi

    # 检查 SELinux 并设置上下文
    if check_selinux; then
        set_selinux_context
    fi

    # 检查目录内容
    log "检查目录内容..."
    ls -la "$OUT_DIR" || err "无法列出目录内容"

    # 检查 Nginx 用户是否有权限访问
    if [ "$NGINX_USER" != "root" ]; then
        # 使用更宽松的权限检查方式
        if ! ls -l "$OUT_DIR" | grep -q "^drwxr-xr-x.*$NGINX_USER"; then
            err "目录权限设置不正确"
        fi

        # 检查 index.html 权限
        if ! ls -l "$OUT_DIR/index.html" | grep -q "^-rw-r--r--.*$NGINX_USER"; then
            log "修复 index.html 权限..."
            sudo chmod 644 "$OUT_DIR/index.html" || err "无法设置 index.html 权限"
            sudo chown $NGINX_USER:$NGINX_USER "$OUT_DIR/index.html" || err "无法设置 index.html 所有者"
        fi
    fi

    # 验证 Nginx 用户是否可以访问目录
    log "验证 Nginx 用户访问权限..."
    if [ "$NGINX_USER" != "root" ]; then
        if ! sudo -u $NGINX_USER ls "$OUT_DIR" > /dev/null 2>&1; then
            err "Nginx 用户无法访问目录"
        fi
        if ! sudo -u $NGINX_USER cat "$OUT_DIR/index.html" > /dev/null 2>&1; then
            err "Nginx 用户无法读取 index.html"
        fi
    fi

    log "目录验证完成"
}

# 检查 Nginx 错误日志
check_nginx_logs() {
    log "检查 Nginx 错误日志..."

    # 等待 Nginx 重新加载完成
    sleep 2

    # 检查错误日志
    if [ -f "/var/log/nginx/error.log" ]; then
        log "最近的错误日志内容:"
        sudo tail -n 20 /var/log/nginx/error.log | while read -r line; do
            log "[ERROR LOG] $line"
        done
    else
        log "错误日志文件不存在: /var/log/nginx/error.log"
    fi

    # 检查访问日志
    if [ -f "/var/log/nginx/access.log" ]; then
        log "最近的访问日志内容:"
        sudo tail -n 10 /var/log/nginx/access.log | while read -r line; do
            log "[ACCESS LOG] $line"
        done
    else
        log "访问日志文件不存在: /var/log/nginx/access.log"
    fi
}

# 配置Nginx
setup_nginx() {
    log "开始配置Nginx..."

    # 检查sudo权限
    log "检查sudo权限..."
    if ! sudo -v &> /dev/null; then
        err "没有sudo权限，无法继续安装"
    fi
    log "sudo权限检查通过"

    # 检查Nginx是否已安装
    log "检查Nginx安装状态..."
    if ! command -v nginx &> /dev/null; then
        log "Nginx未安装，开始安装..."

        # 检测系统类型
        if [ -f /etc/os-release ]; then
            . /etc/os-release
            OS=$NAME
            if [[ "$OS" == *"Alibaba Cloud Linux"* ]]; then
                log "检测到阿里云Linux系统，使用阿里云源安装Nginx..."
                sudo yum install -y nginx || err "yum install nginx失败"
            elif command -v apt-get &> /dev/null; then
                # Debian/Ubuntu
                log "使用apt-get安装Nginx..."
                sudo apt-get update || err "apt-get update失败"
                sudo apt-get install -y nginx || err "apt-get install nginx失败"
            elif command -v yum &> /dev/null; then
                # RHEL/CentOS
                log "使用yum安装Nginx..."
                sudo yum install -y epel-release || err "yum install epel-release失败"
                sudo yum install -y nginx || err "yum install nginx失败"
            elif command -v dnf &> /dev/null; then
                # Fedora
                log "使用dnf安装Nginx..."
                sudo dnf install -y nginx || err "dnf install nginx失败"
            else
                err "不支持的操作系统"
            fi
        else
            err "无法检测操作系统类型"
        fi

        # 验证安装
        if ! command -v nginx &> /dev/null; then
            err "Nginx安装失败"
        fi

        log "Nginx安装完成，版本: $(nginx -v 2>&1)"
    else
        log "Nginx已安装，版本: $(nginx -v 2>&1)"
    fi

    # 配置Nginx
    log "正在配置Nginx..."

    # 检查nginx.conf文件是否存在
    if [ ! -f "$(dirname "$0")/nginx.conf" ]; then
        err "nginx.conf文件不存在"
    fi
    log "找到nginx.conf文件"

    # 备份默认配置
    if [ -f /etc/nginx/nginx.conf ]; then
        log "备份默认Nginx配置..."
        sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak || err "备份Nginx配置失败"
        log "默认配置已备份到/etc/nginx/nginx.conf.bak"
    fi

    # 创建临时配置文件
    log "创建临时Nginx配置..."
    TMP_NGINX_CONF=$(mktemp)
    sed "s|/home/<USER>/app/out|$OUT_DIR|g" "$(dirname "$0")/nginx.conf" > "$TMP_NGINX_CONF" || err "创建临时配置文件失败"

    # 复制配置文件
    log "复制新的Nginx配置..."
    sudo cp "$TMP_NGINX_CONF" /etc/nginx/nginx.conf || err "复制Nginx配置失败"
    rm "$TMP_NGINX_CONF"  # 清理临时文件
    log "新的Nginx配置已复制"


    # 备份默认配置
    if [ -f /home/<USER>/apple-app-site-association ]; then
        log "备份默认IOS universal link配置..."
        sudo cp -f /home/<USER>/apple-app-site-association /home/<USER>/apple-app-site-association.bak || err "备份默认IOS universal link配置失败"
        log "默认配置已备份到/home/<USER>/apple-app-site-association.bak"
    fi

    NGINX_USER=$(get_nginx_user)
    # 复制ios universal link
    sudo cp /home/<USER>/app/scripts/deploy/apple-app-site-association /home/<USER>/
    sudo chown $NGINX_USER:$NGINX_USER "/home/<USER>/apple-app-site-association"

    # 测试配置
    log "测试Nginx配置..."
    if ! sudo nginx -t; then
        err "Nginx配置测试失败"
    fi
    log "Nginx配置测试通过"

    # 启动Nginx
    log "正在启动Nginx..."

    # 检查Nginx是否已经在运行
    if pgrep nginx > /dev/null; then
        log "Nginx已经在运行，重新加载配置..."
        sudo nginx -s reload || err "Nginx重新加载配置失败"
    else
        log "启动Nginx服务..."
        sudo systemctl start nginx || sudo service nginx start || err "Nginx启动失败"
    fi

    # 验证Nginx是否正在运行
    if ! pgrep nginx > /dev/null; then
        err "Nginx启动失败"
    fi

    log "Nginx启动成功"
    log "Nginx配置完成"

    # 检查 Nginx 日志
    check_nginx_logs
}

# 主部署流程
log "开始部署流程..."

# 查找 out 目录
find_out_directory
log "使用 out 目录: $OUT_DIR"

# 收集系统信息
collect_system_info

# 验证目录和权限
verify_directory

# 配置 Nginx
setup_nginx

log "部署完成"

exit 0
