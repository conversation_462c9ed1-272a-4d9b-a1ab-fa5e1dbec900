import { getCookie } from '../cookie';
import { buildQueryString } from '../index';

interface RequestConfig {
  baseURL?: string;
  defaultHeaders?: Record<string, string>;
  timeout?: number;
}

interface IRequestOptions extends RequestInit {
  /** 忽略错误 */
  ignoreError?: boolean;
  /** 超时时间 */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** Get请求参数 */
  params?: Record<string, any>;
  headers?: Record<string, string>;
}

export function createRequest(config: RequestConfig = {}) {
  const { baseURL = '', defaultHeaders = {}, timeout = 10000 } = config;

  async function request<T>(
    url: string,
    options: IRequestOptions = {}
  ): Promise<App.Service.IResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body = null,
      retryCount = 0,
      signal,
      ignoreError,
      timeout: requestTimeout = timeout,
    } = options;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), requestTimeout);

    try {
      const token = getCookie('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      if (method.toUpperCase() === 'GET' && options.params) {
        url += buildQueryString(options.params);
      }
      if (
        ['POST', 'PUT'].includes(method.toUpperCase()) &&
        headers['Content-Type'] === 'application/json' &&
        body
      ) {
        options.body = JSON.stringify(body);
      }
      const response = await fetch(`${baseURL}${url}`, {
        signal: signal || controller.signal,
        ...options,
        headers: { ...defaultHeaders, ...headers },
        body,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `Request to ${baseURL}${url} failed with status ${response.status}: ${response.statusText} - ${errorText}`
        );
        throw new Error(
          `Error ${response.status}: ${response.statusText} - ${errorText}`
        );
      }

      const res = (await response.json()) as App.Service.IResponse<T>;
      if (res.code !== 200 && !ignoreError && typeof window !== 'undefined') {
        window?.alert?.(`Error: ${res.message}`);
      }
      return { ...res, isSuccess: res.code === 200 };
    } catch (error) {
      clearTimeout(timeoutId);

      if (retryCount > 0) {
        return request<T>(url, {
          ...options,
          retryCount: retryCount - 1,
        });
      }

      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
        const apiErrorMatch = errorMessage.match(/^API Error (\d+): (.*)$/);
        if (apiErrorMatch) {
          const code = parseInt(apiErrorMatch[1], 10);
          const message = apiErrorMatch[2];
          errorMessage = `API Error ${code}: ${message}`;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      console.error(`Request to ${baseURL}${url} failed:`, errorMessage);

      return {
        data: null,
        message: errorMessage,
        code: 500,
        isSuccess: false,
      } as App.Service.IResponse<T>;
    }
  }

  return request;
}
