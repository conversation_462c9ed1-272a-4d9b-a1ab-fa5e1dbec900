declare global {
  interface Window {
    __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: any;
  }

  // 确保 React 在全局可用
  const React: typeof import('react');

  // API 命名空间全局声明
  namespace API {
    namespace Common {
      interface PaginatingCommonParams {
        /** Current page number */
        current: number;
        /** Page size */
        size: number;
        /** Total count */
        total: number;
      }

      interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
        list: T[];
      }
    }
  }
}

declare module 'eslint-plugin-import';
declare module 'eslint-plugin-jsx-a11y';

declare module '@typescript-eslint/eslint-plugin' {
  import type { Linter } from 'eslint';

  const plugin: Linter.Plugin;
  export default plugin;
}
