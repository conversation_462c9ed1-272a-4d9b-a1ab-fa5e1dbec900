import { forwardRef } from 'react';

import Image from 'next/image';

import { motion } from 'framer-motion';

import { getImageUrl } from '@/constants/cdn';
import { ABOUT_TEXTS } from '@/constants/texts';

import ScrollArrow from './ScrollArrow';

interface AboutSectionProps {
  scrollToNext: () => void;
  scrollToPrev: () => void;
  onViewportEnter?: () => void;
  viewport?: { amount?: number | 'some' | 'all' };
}

const AboutSectionMobile = forwardRef<HTMLDivElement, AboutSectionProps>(
  ({ scrollToNext, scrollToPrev }, ref) => {
    const sectionVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: { duration: 0.4, ease: 'easeOut' },
      },
    };

    const contentVariants = {
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.6,
          delay: 0.2,
          ease: [0.4, 0, 0.2, 1],
        },
      },
    };

    return (
      <section
        ref={ref}
        className="relative min-h-screen w-full bg-white flex flex-col items-center snap-start py-6 px-4"
      >
        <div className="flex-grow w-full flex flex-col items-center justify-center">
          <motion.div
            className="w-full flex flex-col items-center justify-center"
            variants={sectionVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ amount: 0.3 }}
          >
            <motion.div
              className="w-full flex flex-col items-center"
              variants={contentVariants}
            >
              {/* 图片部分 */}
              <div className="w-full max-w-[280px] mb-4">
                <div className="relative w-full h-[180px]">
                  <Image
                    src={getImageUrl('about.png')}
                    alt="关于我们"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
              </div>
              {/* 文案部分 */}
              <div className="w-full max-w-[280px] flex flex-col items-center">
                <h2 className="text-[24px] font-bold mb-3 text-[#11111E] text-center">
                  {ABOUT_TEXTS.title}
                </h2>
                {ABOUT_TEXTS.content.map((text, index) => (
                  <p
                    key={index}
                    className="text-[12px] leading-[18px] mb-2 text-[#66666E] text-center"
                  >
                    {text}
                  </p>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* 滚动箭头 */}
        <ScrollArrow
          direction="down"
          onClick={scrollToNext}
          className="bottom-2"
        />
        <ScrollArrow direction="up" onClick={scrollToPrev} className="top-2" />
      </section>
    );
  }
);

AboutSectionMobile.displayName = 'AboutSectionMobile';

export default AboutSectionMobile;
