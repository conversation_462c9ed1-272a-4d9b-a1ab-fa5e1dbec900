'use client';

import { createContext, useState, type ReactNode } from 'react';

interface SectionContextType {
  currentSectionIndex: number;
  setCurrentSectionIndex: React.Dispatch<React.SetStateAction<number>>;
}

export const SectionContext = createContext<SectionContextType | undefined>(
  undefined
);

export const SectionProvider = ({ children }: { children: ReactNode }) => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);

  return (
    <SectionContext.Provider
      value={{ currentSectionIndex, setCurrentSectionIndex }}
    >
      {children}
    </SectionContext.Provider>
  );
};
