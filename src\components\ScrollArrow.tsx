import React from 'react';

import { motion } from 'framer-motion';

interface ScrollArrowProps {
  direction: 'down' | 'up';
  onClick: () => void;
  className?: string;
}

const ScrollArrow: React.FC<ScrollArrowProps> = ({
  direction,
  onClick,
  className,
}) => {
  const arrowPath =
    direction === 'down'
      ? 'M12 21L4.5 13.5M12 21L19.5 13.5M12 21L12 3'
      : 'M12 3L4.5 10.5M12 3L19.5 10.5M12 3L12 21';

  return (
    <motion.div
      className={`absolute cursor-pointer p-4 rounded-full bg-white bg-opacity-80 shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 z-10 ${className}`}
      onClick={onClick}
      initial={{ opacity: 0, y: direction === 'down' ? 20 : -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      whileHover={{ scale: 1.1 }}
    >
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={2}
        stroke="currentColor"
        className="w-6 h-6 text-primary-blue"
        animate={{ y: [0, direction === 'down' ? 5 : -5, 0] }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      >
        <path strokeLinecap="round" strokeLinejoin="round" d={arrowPath} />
      </motion.svg>
    </motion.div>
  );
};

export default ScrollArrow;
