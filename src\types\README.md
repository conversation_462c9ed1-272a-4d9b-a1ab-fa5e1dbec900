# 类型声明组织结构

本项目采用模块化的类型声明组织方式，将业务类型按模块拆分，便于管理和维护。

## 📁 文件结构

```
src/types/
├── global.d.ts                 # 全局类型声明
├── api.d.ts                    # 通用 API 类型
├── app.d.ts                    # 应用层类型
├── modules/                    # 业务模块类型
│   ├── _template.d.ts         # 模块模板文件
│   ├── terms.d.ts             # 协议相关类型
│   ├── user.d.ts              # 用户相关类型
│   └── ...                    # 其他业务模块
└── README.md                   # 本文档
```

## 🎯 设计原则

### 1. 分层设计

- **全局层**: `global.d.ts` - 全局类型声明和命名空间
- **通用层**: `api.d.ts` - 跨模块的通用 API 类型
- **应用层**: `app.d.ts` - 应用级别的类型定义
- **业务层**: `modules/*.d.ts` - 具体业务模块的类型

### 2. 命名空间组织

- 使用 `API.模块名` 的方式访问业务类型
- 使用 `App.功能名` 的方式访问应用类型
- 保持类型访问的一致性和可预测性

### 3. 模块化拆分

- 每个业务模块独立维护自己的类型声明
- 避免单个文件过大，提高可维护性
- 便于团队协作和代码审查

## 📋 使用方法

### 访问业务类型

```typescript
// 用户相关类型
const user: API.User.IUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
};

// 协议相关类型
const agreement: API.Terms.IAgreementsFetchResponse = {
  type: 'user',
  title: '用户协议',
  content: [],
  version: '1.0',
  effectiveDate: '2024-01-01',
  isRequired: true,
};
```

### 访问通用类型

```typescript
// 分页参数
const params: API.Common.PaginatingCommonParams = {
  current: 1,
  size: 10,
  total: 100,
};

// 应用响应格式
const response: App.Service.IResponse<API.User.IUser[]> = {
  code: 200,
  message: 'success',
  isSuccess: true,
  data: [],
};
```

## 🔧 新增业务模块

### 1. 创建模块类型文件

```bash
# 复制模板文件
cp src/types/modules/_template.d.ts src/types/modules/product.d.ts
```

### 2. 修改模块声明

```typescript
// src/types/modules/product.d.ts
declare namespace API.Product {
  interface IProduct {
    id: string;
    name: string;
    price: number;
    // ... 其他字段
  }

  interface IProductListResponse {
    products: IProduct[];
    total: number;
  }

  // ... 其他接口
}
```

### 3. 更新类型索引

```typescript
// src/types/index.ts
export * from './modules/product';
```

### 4. 更新全局声明

```typescript
// src/types/global.d.ts
declare global {
  namespace API {
    // ...
    namespace Product {} // 新增命名空间声明
  }
}
```

## 📝 命名规范

### 接口命名

- 使用 `I` 前缀：`IUser`, `IProduct`
- 明确类型用途：
  - `IXxxResponse` - 响应类型
  - `IXxxRequest` - 请求类型
  - `IXxxParams` - 参数类型
  - `IXxxProps` - 组件属性类型

### 命名空间

- 使用 PascalCase：`API.User`, `API.Product`
- 保持简洁明了，避免过深的嵌套
- 按业务模块划分，而不是按技术层面

## 🛠️ 最佳实践

### 1. 类型复用

```typescript
// 继承通用类型
interface IProductQueryParams extends API.Common.PaginatingCommonParams {
  category?: string;
  keyword?: string;
}
```

### 2. 泛型使用

```typescript
// 利用泛型提高复用性
interface IListResponse<T> {
  list: T[];
  total: number;
}
```

### 3. 可选字段处理

```typescript
// 合理使用可选字段
interface ICreateUserRequest {
  name: string; // 必填
  email: string; // 必填
  avatar?: string; // 可选
}
```

### 4. 类型注释

```typescript
// 添加详细的 JSDoc 注释
/** 用户基础信息 */
interface IUser {
  /** 用户唯一标识 */
  id: string;
  /** 用户显示名称 */
  name: string;
}
```

## 🔍 IDE 支持

### VSCode 配置

项目已配置 TypeScript 路径映射，支持：

- 自动补全：`API.User.` 会提示可用接口
- 跳转定义：Ctrl/Cmd + Click 跳转到类型定义
- 类型检查：实时检查类型使用是否正确

### 推荐插件

- TypeScript Importer
- Auto Rename Tag
- Path Intellisense

## 🚀 维护建议

1. **定期审查**: 定期检查类型使用情况，移除无用类型
2. **版本管理**: 重大类型变更时考虑版本控制
3. **文档同步**: 类型变更后及时更新相关文档
4. **测试覆盖**: 重要类型变更后进行充分测试

---

通过这种模块化的类型组织方式，可以有效提高代码的可维护性和开发效率。
