import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
import express from "express";
import cors from "cors";
import fs from "fs";
import path from "path";

const app = express();
const port = 4000;
const mockDir = __dirname; // Mock data directory

// 启用CORS, 允许所有来源
app.use(cors());

// 解析JSON请求体
app.use(express.json());

// 读取mock数据
const readMockData = (filepath) => { // 修改参数名为 filepath
  try {
    const data = fs.readFileSync(filepath, "utf8"); // 直接使用完整路径
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading mock data from ${filepath}:`, error);
    return null;
  }
};

// 递归查找并创建路由
const createRoutes = (directory) => {
  fs.readdirSync(directory, { withFileTypes: true }).forEach((dirent) => {
    const fullPath = path.join(directory, dirent.name);
    if (dirent.isDirectory()) {
      // 如果是目录，递归调用
      createRoutes(fullPath);
    } else if (dirent.isFile() && dirent.name.endsWith(".json")) {
      // 如果是 .json 文件，创建路由
      const relativePath = path.relative(mockDir, fullPath); // 获取相对于 mockDir 的路径
      const routePathWithoutExtension = path.join('/api', relativePath.replace(/\.json$/, '')); // 构建路由路径，替换 .json
      const routePath = routePathWithoutExtension.replace(/\\/g, '/'); // 将 Windows 路径分隔符替换为 '/'

      app.post(routePath, (req, res) => {
        console.log(`Received POST request for ${routePath}`);
        const data = {
          code: 200,
          message: "Success",
          data: readMockData(fullPath), // 传递完整路径
        };
        if (data) {
          res.json(data);
        } else {
          res.status(500).json({ error: "Failed to read mock data" });
        }
      });

      console.log(`Created POST route: ${routePath}`);
    }
  });
};

// 开始创建路由
createRoutes(mockDir);


// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: "Something broke!" });
});

// 启动服务器
app.listen(port, () => {
  console.log(`Mock server is running at http://localhost:${port}`);
});
