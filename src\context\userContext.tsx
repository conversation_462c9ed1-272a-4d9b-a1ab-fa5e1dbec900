'use client';

import type { ReactNode } from 'react';
// eslint-disable-next-line no-duplicate-imports
import { createContext, useContext, useState } from 'react';

interface UserContextType {
  users: API.User.IUser[];
  // eslint-disable-next-line no-unused-vars
  setUsers: (users: API.User.IUser[]) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [users, setUsers] = useState<API.User.IUser[]>([]);

  return (
    <UserContext.Provider value={{ users, setUsers }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
