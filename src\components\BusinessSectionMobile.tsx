import React, { forwardRef } from 'react';

import { motion } from 'framer-motion';

import { getImageUrl } from '@/constants/cdn';
import { BUSINESS_TEXTS } from '@/constants/texts';

import BeianInfo from './BeianInfo';
import ScrollArrow from './ScrollArrow';

interface BusinessSectionProps {
  scrollToPrev: () => void;
  onViewportEnter?: () => void;
  viewport?: { amount?: number | 'some' | 'all' };
}

interface BusinessItemProps {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
  style?: React.CSSProperties;
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: 'easeOut' } },
};

const BusinessItem: React.FC<BusinessItemProps> = ({
  icon,
  title,
  description,
  className,
  style,
}) => (
  <motion.div
    className={`flex flex-col items-center text-center p-3 bg-white rounded-lg shadow-md w-full max-w-[280px] transition-all duration-300 hover:shadow-lg ${className ?? ''}`}
    variants={itemVariants}
    style={style}
  >
    <div className="w-8 h-8 mb-2 flex items-center justify-center mt-1">
      {icon}
    </div>
    <h3 className="text-[16px] text-[#11111E] leading-[24px] h-[24px] font-bold mb-1 mt-0">
      {title}
    </h3>
    <p
      className="text-[12px] text-[#66666E] leading-[18px] mt-0 mb-0 text-left"
      style={{
        marginBlockStart: '0px',
        marginBlockEnd: '0px',
      }}
    >
      {description}
    </p>
  </motion.div>
);

const BusinessSectionMobile = forwardRef<HTMLDivElement, BusinessSectionProps>(
  ({ scrollToPrev }, ref) => {
    const sectionVariants = {
      hidden: { opacity: 0, y: 50 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.3,
          ease: 'easeOut',
          when: 'beforeChildren',
          staggerChildren: 0.1,
        },
      },
    };

    const businessItems = BUSINESS_TEXTS.items.map(item => ({
      id: item.id,
      icon: (
        <div className="w-[32px] h-[32px] flex items-center justify-center">
          <img
            src={getImageUrl(item.icon)}
            alt={item.title}
            className="w-[32px] h-[32px] object-contain"
          />
        </div>
      ),
      title: item.title,
      description: item.description,
    }));

    return (
      <section
        ref={ref}
        className="relative min-h-screen w-full flex flex-col items-center justify-start snap-start py-2 px-4 pb-16 text-text-dark"
        style={{ background: '#f9f9f9' }}
      >
        <motion.div
          className="flex flex-col items-center w-full mt-8"
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ amount: 0.8 }}
        >
          <h2 className="text-[20px] mt-[20%] text-[#11111E] font-bold h-[28px] leading-[28px] text-center mt-0 mb-3">
            {BUSINESS_TEXTS.title}
          </h2>
          <div className="flex flex-col items-center gap-2 w-full">
            {businessItems.map(item => (
              <BusinessItem key={item.id} {...item} className="business-item" />
            ))}
          </div>
        </motion.div>
        <ScrollArrow
          direction="up"
          onClick={scrollToPrev}
          className="bottom-4"
        />

        {/* 页脚备案信息 */}
        <div
          className="absolute bottom-0 left-0 right-0 w-full py-2"
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
          }}
        >
          <BeianInfo textClassName="text-[12px] text-[#66666E]" />
        </div>
      </section>
    );
  }
);

BusinessSectionMobile.displayName = 'BusinessSectionMobile';
export default BusinessSectionMobile;
