declare namespace API.Terms {
  /** 协议获取响应 */
  interface IAgreementsFetchResponse {
    /** 协议类型 */
    type: 'user' | 'privacy' | 'content';
    /** 协议标题 */
    title: string;
    /** 协议内容 */
    content: IAgreementsContent[];
    /** 版本号 */
    version: string;
    /** 生效日期 */
    effectiveDate: string;
    /** 是否必需 */
    isRequired: boolean;
  }

  /** 协议内容结构 */
  interface IAgreementsContent {
    /** 章节标题 */
    title: string;
    /** 协议条款列表 */
    agreements: string[];
    /** 扩展字段 */
    [key: string]: string | string[] | undefined;
  }

  /** 协议页面参数 */
  interface IAgreementPageProps {
    /** 协议ID */
    id: string;
    /** 扩展参数 */
    [key: string]: string | string[] | undefined;
  }
}
