import { Inter } from 'next/font/google';

import Navbar from '@/components/Navbar';
import { getImageUrl } from '@/constants/cdn';
import { SectionProvider } from '@/context';
import './globals.css';

import type { Metadata } from 'next';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '翎游 - 旅游 出行 旅行 自由行 行程定制',
  description:
    '翎游是AI+旅游领域的创新科技平台，通过自研垂类大模型与多模态交互系统，打造AI行程管家，提供行前决策、行中优化、行后延伸的全周期智慧旅游服务。面向景区及目的地，推出数字文旅大脑驱动行业效能升级。现已布局一二线高净值市场与跨境网络，获头部资本战略投资，致力于以AI重构人文旅行体验。',
  icons: {
    icon: getImageUrl('favicon.ico'),
  },
};

const RootLayout = ({ children }: { children: React.ReactNode }) => (
  <html lang="zh" className="scroll-smooth">
    <body className={inter.className}>
      <SectionProvider>
        <Navbar />
        {children}
      </SectionProvider>
    </body>
  </html>
);

export default RootLayout;
