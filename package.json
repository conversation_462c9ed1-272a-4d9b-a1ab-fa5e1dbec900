{"name": "ota-app-web", "version": "0.0.1", "type": "module", "scripts": {"dev:mock": "concurrently -k \"nodemon mock/server.js\" \"pnpm run dev\"", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings=99999", "fix": "next lint --fix --max-warnings=99999", "cleanup": "tsx ./scripts/bin.ts cleanup", "commit": "tsx ./scripts/bin.ts git-commit", "commit:zh": "tsx ./scripts/bin.ts git-commit -l=zh-cn", "gen-route": "tsx ./scripts/bin.ts gen-route", "release": "tsx ./scripts/bin.ts release", "git-commit-verify": "tsx ./scripts/bin.ts git-commit-verify", "prepare": "simple-git-hooks", "typecheck": "tsc --project tsconfig.json --noEmit --skipLib<PERSON><PERSON>ck", "update-pkg": "tsx ./scripts/bin.ts update-pkg"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "dependencies": {"framer-motion": "^12.16.0", "motion": "^12.16.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.27.0", "@soybeanjs/changelog": "0.3.24", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/yargs": "17.0.33", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "bumpp": "10.0.3", "c12": "2.0.4", "cac": "6.7.14", "concurrently": "^9.1.2", "consola": "3.4.0", "cors": "^2.8.5", "enquirer": "2.4.1", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-sort": "4.0.0", "eslint-plugin-unused-imports": "^4.1.4", "execa": "9.5.2", "express": "^4.18.2", "globals": "^16.1.0", "kolorist": "1.8.0", "lint-staged": "^16.0.0", "nodemon": "^3.0.2", "npm-check-updates": "17.1.14", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "rimraf": "6.0.1", "simple-git-hooks": "2.11.1", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5", "yargs": "17.7.2"}, "simple-git-hooks": {"commit-msg": "pnpm git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix --max-warnings=99999"}}