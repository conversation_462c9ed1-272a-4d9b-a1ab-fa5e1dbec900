declare namespace API.User {
  /** 用户基础信息 */
  interface IUser {
    /** 用户ID */
    id: string | number;
    /** 用户名称 */
    name: string;
    /** 用户邮箱 */
    email?: string;
    /** 用户头像 */
    avatar?: string;
    /** 用户状态 */
    status?: 'active' | 'inactive' | 'pending';
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  }

  /** 用户列表响应 */
  interface IPostsUserResponse {
    /** 用户列表 */
    users: IUser[];
    /** 总数量 */
    total?: number;
  }

  /** 用户详情响应 */
  interface IUserDetailResponse {
    /** 用户详细信息 */
    user: IUser;
  }

  /** 用户创建请求 */
  interface ICreateUserRequest {
    /** 用户名称 */
    name: string;
    /** 用户邮箱 */
    email: string;
    /** 用户密码 */
    password?: string;
  }

  /** 用户更新请求 */
  interface IUpdateUserRequest {
    /** 用户名称 */
    name?: string;
    /** 用户邮箱 */
    email?: string;
    /** 用户头像 */
    avatar?: string;
    /** 用户状态 */
    status?: 'active' | 'inactive' | 'pending';
  }
}
