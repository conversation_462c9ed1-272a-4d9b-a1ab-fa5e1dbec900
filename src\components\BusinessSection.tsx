import React, { forwardRef, useEffect, useState } from 'react';

import { motion } from 'framer-motion';

import { getImageUrl } from '@/constants/cdn';
import { BUSINESS_TEXTS } from '@/constants/texts';

import BeianInfo from './BeianInfo';
import BusinessSectionMobile from './BusinessSectionMobile';
import ScrollArrow from './ScrollArrow';

interface BusinessSectionProps {
  scrollToPrev: () => void;
  onViewportEnter?: () => void;
  viewport?: { amount?: number | 'some' | 'all' };
}

interface BusinessItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
  style?: React.CSSProperties;
}

const BusinessItem = ({
  icon,
  title,
  description,
  className,
  style,
}: BusinessItemProps) => (
  <motion.div
    className={`flex flex-col items-start ${className}`}
    style={style}
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.5, delay: 0.2 }}
  >
    {icon}
    <h3 className="text-[18px] font-bold text-[#11111E] mt-[16px] mb-[8px]">
      {title}
    </h3>
    <p className="text-[14px] leading-[22px] text-[#66666E]">{description}</p>
  </motion.div>
);

const BusinessSection = forwardRef<HTMLDivElement, BusinessSectionProps>(
  ({ scrollToPrev }, ref) => {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth <= 768);
      };

      checkMobile();
      window.addEventListener('resize', checkMobile);

      return () => {
        window.removeEventListener('resize', checkMobile);
      };
    }, []);

    if (isMobile) {
      return <BusinessSectionMobile scrollToPrev={scrollToPrev} ref={ref} />;
    }

    const sectionVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          duration: 0.8,
          staggerChildren: 0.2,
        },
      },
    };

    const businessItems = BUSINESS_TEXTS.items.map(item => ({
      icon: (
        <div className="w-[48px] h-[48px] flex items-center justify-center">
          <img
            src={getImageUrl(item.icon)}
            alt={item.title}
            className="w-[48px] h-[48px] object-contain"
          />
        </div>
      ),
      title: item.title,
      description: item.description,
    }));

    return (
      <section
        ref={ref}
        className="relative h-screen w-full flex flex-col items-center justify-center snap-start py-16 px-8 text-text-dark"
        style={{ background: '#f9f9f9' }}
      >
        <motion.div
          className="flex flex-col"
          style={{ maxWidth: '1310px', width: '100%', margin: '0 auto' }}
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ amount: 0.8 }}
          onViewportEnter={() => {
            const elements = document.querySelectorAll('.business-item');
            elements.forEach(el => {
              el.classList.add('animate-none');
            });
          }}
        >
          <h2 className="text-[36px] text-[#11111E] font-bold h-[50px] leading-[50px] text-left mt-[0px] mb-[36px]">
            {BUSINESS_TEXTS.title}
          </h2>
          <div className="flex flex-row justify-between w-full">
            {businessItems.map((item, index) => (
              <BusinessItem
                key={index}
                {...item}
                className="business-item flex flex-col items-start text-left"
                style={{ width: '257px' }}
              />
            ))}
          </div>
        </motion.div>
        <ScrollArrow
          direction="up"
          onClick={scrollToPrev}
          className="bottom-8"
        />
        {/* 页脚备案信息 */}
        <motion.div className="absolute bottom-[54px] w-full">
          <BeianInfo textClassName="text-[14px] text-[#66666E]" />
        </motion.div>
      </section>
    );
  }
);

BusinessSection.displayName = 'BusinessSection';
export default BusinessSection;
