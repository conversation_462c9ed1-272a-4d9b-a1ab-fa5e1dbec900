import React from 'react';

import Image from 'next/image';

import { BEIAN } from '@/constants/beian';
import { getImageUrl } from '@/constants/cdn';

interface BeianInfoProps {
  className?: string;
  textClassName?: string;
}

const BeianInfo: React.FC<BeianInfoProps> = ({
  className = '',
  textClassName = '',
}) => (
  <div className={`w-full text-center ${className}`}>
    <p className={`mt-0 mb-0 ${textClassName}`}>
      <a
        href={BEIAN.link}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-500 hover:underline"
      >
        <Image
          className="gov-icon"
          src={getImageUrl('gov.png')}
          alt="关于我们"
          fill
          priority
        />
        {BEIAN.number}
      </a>
    </p>
  </div>
);

export default BeianInfo;
