'use client';

import Image from 'next/image';

import { getImageUrl } from '@/constants/cdn';
import { useSection } from '@/hooks/useSection';

const Navbar = () => {
  const { currentSectionIndex } = useSection();
  const isFirstPage = currentSectionIndex === 0;

  return (
    <nav className="fixed top-[27px] left-0 right-0 h-16 z-50 w-full">
      <div className="h-full max-w-[calc(100%-80px)] mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <Image
            src={
              isFirstPage
                ? getImageUrl('logo_w_bg.png')
                : getImageUrl('logo_bg.png')
            }
            alt="Logo"
            width={114}
            height={32}
            className="object-contain"
            style={
              isFirstPage
                ? {}
                : { filter: 'drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1))' }
            }
            priority
          />
        </div>
        <div
          className={`text-[18px] font-bold ${
            isFirstPage ? 'text-[#fff]' : 'text-[#000]'
          }`}
          style={{ marginRight: '10px' }}
        >
          Hi 你好
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
