# EditorConfig is awesome: https://EditorConfig.org

# 顶层配置文件
root = true

# 所有文件
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# JavaScript/TypeScript 文件
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2

# CSS/SCSS 文件
[*.{css,scss}]
indent_style = space
indent_size = 2

# HTML 文件
[*.html]
indent_style = space
indent_size = 2

# JSON 文件
[*.json]
indent_style = space
indent_size = 2

# YAML 文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# Shell 脚本
[*.{sh,bash}]
end_of_line = lf
indent_style = space
indent_size = 2
