// /home/<USER>/app/scripts/deploy/ecosystem.config.cjs
module.exports = {
  // <--- 改为 module.exports
  apps: [
    {
      name: 'ota-app-web',
      script: process.env.PNPM_PATH || 'pnpm',
      args: 'start',
      interpreter: 'none',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        PNPM_HOME: process.env.PNPM_HOME || '/root/.local/share/pnpm',
        PATH: `${process.env.PNPM_HOME || '/root/.local/share/pnpm'}:${process.env.PATH || '/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'}`,
        PNPM_PATH: process.env.PNPM_PATH,
      },
      cwd: '/home/<USER>/app',
    },
  ],
};
